import { NextResponse } from 'next/server';
import pbclient from '@/lib/db';

export async function POST(request) {
  try {
    const formData = await request.formData();
    const content = formData.get('content');
    const senderId = formData.get('senderId');
    const chatId = formData.get('chatId');
    const attachments = formData.getAll('attachments');

    if (!senderId || !chatId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }


    // Verify chat exists and user has access
    const chat = await pbclient.collection('customer_client_chat').getOne(chatId);
    if (chat.customer !== senderId && chat.client !== senderId) {
      return NextResponse.json(
        { error: 'Unauthorized to send message in this chat' },
        { status: 403 }
      );
    }

    // Upload attachments if any
    let attachmentIds = [];
    if (attachments && attachments.length > 0) {
      for (const file of attachments) {
        if (file.size > 0) {
          const formData = new FormData();
          formData.append('file', file);
          
          const record = await pbclient.collection('message_attachments').create(formData);
          attachmentIds.push(record.id);
        }
      }
    }

    // Create the message
    const message = await pbclient.collection('customer_client_messages').create({
      chat: chatId,
      sender: senderId,
      content: content || '',
      attachments: attachmentIds,
      isRead: false,
    }, {
      expand: 'sender,attachments',
    });

    // Update chat's last message timestamp
    await pbclient.collection('customer_client_chat').update(chatId, {
      lastMessage: content ? content.substring(0, 100) : 'Attachment',
      lastMessageAt: new Date().toISOString(),
      unreadCount: chat.customer === senderId ? 0 : (chat.unreadCount || 0) + 1,
    });

    // Format the response
    const formattedMessage = {
      id: message.id,
      content: message.content,
      senderId: message.sender,
      senderName: message.expand?.sender?.name || 'You',
      attachments: (message.expand?.attachments || []).map(att => ({
        id: att.id,
        name: att.filename,
        url: `${process.env.NEXT_PUBLIC_POCKETBASE_URL}/api/files/${att.collectionId}/${att.id}/${att.filename}`,
        type: att.type,
      })),
      createdAt: message.created,
      updatedAt: message.updated,
    };

    return NextResponse.json(formattedMessage);
  } catch (error) {
    console.error('Error sending message:', error);
    return NextResponse.json(
      { error: 'Failed to send message' },
      { status: 500 }
    );
  }
}
