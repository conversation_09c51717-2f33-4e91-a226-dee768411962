import { NextResponse } from 'next/server';
import pbclient from '@/lib/db';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    
    // Fetch chat sessions where the user is either the customer or the client
    const sessions = await pbclient.collection('customer_client_chat').getList(1, 50, {
      filter: `customer = "${userId}"`,
      expand: 'client,customer,order',
      sort: '-updated',
    });

    // Format the response
    const formattedSessions = sessions.items.map(session => ({
      id: session.id,
      clientId: session.client,
      clientName: session.expand?.client?.name || 'Client',
      orderId: session.order,
      orderNumber: session.expand?.order?.orderNumber,
      lastMessage: session.lastMessage,
      unreadCount: session.unreadCount || 0,
      updatedAt: session.updated,
      createdAt: session.created,
    }));

    return NextResponse.json(formattedSessions);
  } catch (error) {
    console.error('Error fetching client chat sessions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch chat sessions' },
      { status: 500 }
    );
  }
}
