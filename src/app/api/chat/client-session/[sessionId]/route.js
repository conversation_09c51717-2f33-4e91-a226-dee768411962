import { NextResponse } from 'next/server';
import pbclient from '@/lib/db';

export async function GET(request, { params }) {
  try {
    const { sessionId } = params;
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Fetch the chat session
    const chat = await pbclient.collection('customer_client_chat').getOne(sessionId, {
      expand: 'client,customer,order',
    });

    // Verify user has access to this chat
    if (chat.customer !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized access to chat' },
        { status: 403 }
      );
    }

    // Fetch messages for this chat
    const messages = await pbclient.collection('customer_client_messages').getList(1, 100, {
      filter: `chat = "${sessionId}"`,
      sort: 'created',
      expand: 'sender,attachments',
    });

    // Format the response
    const formattedChat = {
      id: chat.id,
      clientId: chat.client,
      clientName: chat.expand?.client?.name || 'Client',
      clientEmail: chat.expand?.client?.email,
      orderId: chat.order,
      orderNumber: chat.expand?.order?.orderNumber,
      status: chat.status,
      createdAt: chat.created,
      updatedAt: chat.updated,
    };

    const formattedMessages = messages.items.map(msg => ({
      id: msg.id,
      content: msg.content,
      senderId: msg.sender,
      senderName: msg.expand?.sender?.name || 'Unknown',
      attachments: msg.expand?.attachments?.map(att => ({
        id: att.id,
        name: att.filename,
        url: `${process.env.NEXT_PUBLIC_POCKETBASE_URL}/api/files/${att.collectionId}/${att.id}/${att.filename}`,
        type: att.type,
      })) || [],
      createdAt: msg.created,
      updatedAt: msg.updated,
    }));

    return NextResponse.json({
      chat: formattedChat,
      messages: formattedMessages,
    });
  } catch (error) {
    console.error('Error fetching chat session:', error);
    return NextResponse.json(
      { error: 'Failed to fetch chat session' },
      { status: 500 }
    );
  }
}
